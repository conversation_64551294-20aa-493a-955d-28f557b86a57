import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, FolderOpen, Tags, Eye } from 'lucide-react'

// Mock data - in a real app, this would come from your database
const stats = {
  totalPosts: 24,
  publishedPosts: 18,
  draftPosts: 6,
  totalCategories: 8,
  totalTags: 32,
  totalViews: 15420,
}

const recentPosts = [
  {
    id: '1',
    title: 'Getting Started with Next.js Blog Development',
    status: 'published',
    publishedAt: '2024-01-15',
    views: 1250,
  },
  {
    id: '2',
    title: 'Building Responsive Layouts with Tailwind CSS',
    status: 'published',
    publishedAt: '2024-01-10',
    views: 890,
  },
  {
    id: '3',
    title: 'State Management in React Applications',
    status: 'draft',
    publishedAt: null,
    views: 0,
  },
  {
    id: '4',
    title: 'Advanced TypeScript Patterns',
    status: 'published',
    publishedAt: '2024-01-05',
    views: 1420,
  },
]

export default function AdminDashboard() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to your blog admin panel. Here's an overview of your content.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPosts}</div>
            <p className="text-xs text-muted-foreground">
              {stats.publishedPosts} published, {stats.draftPosts} drafts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCategories}</div>
            <p className="text-xs text-muted-foreground">
              Active categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tags</CardTitle>
            <Tags className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTags}</div>
            <p className="text-xs text-muted-foreground">
              Total tags used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              All time views
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Posts */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Posts</CardTitle>
          <CardDescription>
            Your latest blog posts and their performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentPosts.map((post) => (
              <div
                key={post.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="space-y-1">
                  <h3 className="font-medium">{post.title}</h3>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        post.status === 'published'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {post.status}
                    </span>
                    {post.publishedAt && (
                      <span>Published {post.publishedAt}</span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">{post.views} views</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to manage your blog
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <a
              href="/admin/posts/new"
              className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-muted transition-colors"
            >
              <FileText className="h-8 w-8 text-primary" />
              <div>
                <h3 className="font-medium">Create New Post</h3>
                <p className="text-sm text-muted-foreground">
                  Write a new blog article
                </p>
              </div>
            </a>

            <a
              href="/admin/categories"
              className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-muted transition-colors"
            >
              <FolderOpen className="h-8 w-8 text-primary" />
              <div>
                <h3 className="font-medium">Manage Categories</h3>
                <p className="text-sm text-muted-foreground">
                  Organize your content
                </p>
              </div>
            </a>

            <a
              href="/admin/settings"
              className="flex items-center space-x-3 p-4 border rounded-lg hover:bg-muted transition-colors"
            >
              <Tags className="h-8 w-8 text-primary" />
              <div>
                <h3 className="font-medium">Site Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Configure your blog
                </p>
              </div>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
