# Setup Guide

This guide will walk you through setting up the Next.js Blog Boilerplate from scratch.

## Prerequisites

Before you begin, make sure you have:

- **Node.js 18+** installed
- **npm** or **yarn** package manager
- **Git** for version control
- A **Supabase** account (free tier available)
- A **Vercel** account for deployment (optional)

## Step 1: Project Setup

### Clone the Repository

```bash
git clone <your-repository-url>
cd blog-boilerplate
```

### Install Dependencies

```bash
npm install
```

This will install all required dependencies including:
- Next.js 14
- TypeScript
- Tailwind CSS
- Prisma
- NextAuth.js
- TipTap editor
- And more...

## Step 2: Database Setup with Supabase

### Create a Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up or log in to your account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: Your blog name
   - **Database Password**: Choose a strong password
   - **Region**: Select the closest region to your users

### Get Database Connection String

1. In your Supabase dashboard, go to **Settings** → **Database**
2. Copy the connection string under "Connection string"
3. Replace `[YOUR-PASSWORD]` with your actual database password

### Configure Environment Variables

1. Copy the environment template:
   ```bash
   cp .env.example .env.local
   ```

2. Fill in your Supabase credentials:
   ```env
   # Database
   DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres"

   # Supabase
   NEXT_PUBLIC_SUPABASE_URL="https://[PROJECT-REF].supabase.co"
   NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-key"
   SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
   ```

3. Find your Supabase keys:
   - Go to **Settings** → **API**
   - Copy the **URL** and **anon public** key
   - Copy the **service_role** key (keep this secret!)

## Step 3: Authentication Setup

### Configure NextAuth.js

1. Generate a secret for NextAuth:
   ```bash
   openssl rand -base64 32
   ```

2. Add to your `.env.local`:
   ```env
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-generated-secret"
   ```

### Set Admin Credentials

```env
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password"
```

**Important**: Change these to your actual admin credentials!

## Step 4: Database Schema

### Generate Prisma Client

```bash
npm run db:generate
```

### Push Schema to Database

```bash
npm run db:push
```

This will create all necessary tables in your Supabase database:
- Users
- Posts
- Categories
- Tags
- Media
- Sessions
- Accounts

### Verify Database Setup

1. Go to your Supabase dashboard
2. Navigate to **Table Editor**
3. You should see all the tables created

## Step 5: Site Configuration

### Update Site Settings

Edit `src/lib/config.ts`:

```typescript
export const siteConfig: SiteConfig = {
  name: 'Your Blog Name',
  description: 'Your blog description',
  url: 'http://localhost:3000', // Change for production
  ogImage: '/og-image.jpg',
  links: {
    twitter: 'https://twitter.com/yourusername',
    github: 'https://github.com/yourusername',
    instagram: 'https://instagram.com/yourusername',
  },
}
```

### Customize Theme (Optional)

Edit the theme colors in `src/lib/config.ts`:

```typescript
export const defaultTheme: ThemeConfig = {
  colors: {
    primary: '#3B82F6',      // Blue
    secondary: '#64748B',    // Gray
    accent: '#F59E0B',       // Amber
    background: '#FFFFFF',   // White
    foreground: '#0F172A',   // Dark
  },
  // ... other theme settings
}
```

## Step 6: Start Development

### Run the Development Server

```bash
npm run dev
```

### Access Your Blog

- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin
- **Login**: Use the credentials from your `.env.local`

## Step 7: Create Your First Admin User

### Option 1: Database Direct Insert

1. Open Supabase dashboard
2. Go to **Table Editor** → **User** table
3. Insert a new row:
   ```sql
   INSERT INTO "User" (id, email, name, role) 
   VALUES ('admin-id', '<EMAIL>', 'Admin User', 'ADMIN');
   ```

### Option 2: Using Prisma Studio

```bash
npm run db:studio
```

1. Open the User model
2. Add a new record with role set to 'ADMIN'

## Step 8: Test Your Setup

### Verify Frontend

1. Visit http://localhost:3000
2. You should see the blog homepage
3. Navigation should work properly

### Verify Admin Panel

1. Visit http://localhost:3000/admin
2. Log in with your admin credentials
3. You should see the admin dashboard
4. Try creating a test post

### Test Content Creation

1. Go to **Posts** → **Create New Post**
2. Write a test article
3. Add a category and tags
4. Publish the post
5. View it on the frontend

## Troubleshooting

### Common Issues

**Database Connection Error**
- Check your DATABASE_URL format
- Verify Supabase project is active
- Ensure password is correct

**Authentication Not Working**
- Verify NEXTAUTH_SECRET is set
- Check NEXTAUTH_URL matches your domain
- Ensure admin user exists in database

**Styling Issues**
- Run `npm run build` to check for CSS errors
- Verify Tailwind CSS is properly configured

**TypeScript Errors**
- Run `npm run db:generate` to update Prisma types
- Check for missing dependencies

### Getting Help

If you encounter issues:

1. Check the console for error messages
2. Verify all environment variables are set
3. Ensure database schema is up to date
4. Check the GitHub issues for similar problems

## Next Steps

Once your setup is complete:

1. **Customize the design** to match your brand
2. **Create content categories** for your blog
3. **Write your first blog post**
4. **Configure SEO settings**
5. **Set up deployment** (see DEPLOYMENT.md)

Congratulations! Your Next.js blog is now ready for development. 🎉
