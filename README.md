# Next.js Blog Boilerplate

A comprehensive, production-ready blog boilerplate built with Next.js 14, TypeScript, Tailwind CSS, and Supabase. This boilerplate provides a complete content management system with a modern, responsive design that can be easily customized for multiple blog websites.

## ✨ Features

### 🔐 Admin Panel
- Secure authentication with NextAuth.js
- Complete admin dashboard
- Content management interface
- User role management

### 📝 Content Management
- Rich text editor with TipTap
- Article publishing with categories
- Tag system for content organization
- Draft and published post states
- SEO-friendly URLs with slugs

### 🎨 Rich Media Support
- Image upload and optimization
- Video embedding
- Social media embeds (Instagram, Twitter, YouTube)
- Responsive image handling

### 🎯 Modern Architecture
- **Next.js 14** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Prisma** as ORM
- **Supabase** for database and authentication
- **Modular design** for easy customization

### 📱 Responsive Design
- Mobile-first approach
- Optimized for all screen sizes
- Modern UI components
- Accessible design patterns

### 🚀 Performance
- Image optimization with Next.js Image
- Lazy loading
- SEO optimization
- Fast page loads

## 🛠️ Tech Stack

- **Framework:** Next.js 14 (App Router)
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **Database:** PostgreSQL (via Supabase)
- **ORM:** Prisma
- **Authentication:** NextAuth.js
- **Rich Text Editor:** TipTap
- **UI Components:** Custom components with Radix UI primitives
- **Icons:** Lucide React
- **Deployment:** Vercel (recommended)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- PostgreSQL database (Supabase recommended)

### 1. Clone and Install

```bash
git clone <your-repo-url>
cd blog-boilerplate
npm install
```

### 2. Environment Setup

Copy the environment variables template:

```bash
cp .env.example .env.local
```

Fill in your environment variables:

```env
# Database
DATABASE_URL="your-postgresql-connection-string"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Admin credentials
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password"

# Site configuration
NEXT_PUBLIC_SITE_NAME="My Blog"
NEXT_PUBLIC_SITE_DESCRIPTION="A modern blog built with Next.js"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
```

### 3. Database Setup

Generate Prisma client and push the schema:

```bash
npm run db:generate
npm run db:push
```

### 4. Create Admin User

Run the development server and create your first admin user through the database or use the provided seed script.

### 5. Start Development

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see your blog!

## 📁 Project Structure

```
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── admin/             # Admin panel pages
│   │   ├── blog/              # Public blog pages
│   │   ├── api/               # API routes
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable components
│   │   ├── admin/             # Admin-specific components
│   │   ├── blog/              # Blog-specific components
│   │   ├── ui/                # Base UI components
│   │   └── layout/            # Layout components
│   ├── lib/                   # Utilities and configurations
│   ├── hooks/                 # Custom React hooks
│   ├── types/                 # TypeScript type definitions
│   └── styles/                # Additional styles
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets
└── docs/                      # Documentation
```

## 🎨 Customization

### Theme Customization

The boilerplate uses a modular theme system that makes it easy to customize for different brands:

1. **Colors**: Edit `src/lib/config.ts` to change the color scheme
2. **Fonts**: Modify font imports in `src/app/layout.tsx`
3. **Spacing**: Adjust container widths and spacing in the theme config
4. **Components**: All UI components are in `src/components/ui/` for easy customization

### Site Configuration

Update your site settings in `src/lib/config.ts`:

```typescript
export const siteConfig: SiteConfig = {
  name: 'Your Blog Name',
  description: 'Your blog description',
  url: 'https://yourdomain.com',
  ogImage: '/og-image.jpg',
  links: {
    twitter: 'https://twitter.com/yourusername',
    github: 'https://github.com/yourusername',
    instagram: 'https://instagram.com/yourusername',
  },
}
```

### Adding New Features

The modular architecture makes it easy to add new features:

1. **New Content Types**: Extend the Prisma schema
2. **Custom Components**: Add to the appropriate component directory
3. **API Routes**: Create new routes in `src/app/api/`
4. **Admin Pages**: Add new admin pages in `src/app/admin/`

## 📝 Content Management

### Creating Posts

1. Access the admin panel at `/admin`
2. Navigate to "Posts" → "Create New Post"
3. Use the rich text editor to write your content
4. Add categories, tags, and featured images
5. Save as draft or publish immediately

### Managing Categories

Categories help organize your content:

1. Go to "Categories" in the admin panel
2. Create new categories with custom colors
3. Assign categories to posts
4. Categories appear in navigation and post cards

### Media Management

Upload and manage media files:

1. Use the rich text editor's media buttons
2. Upload images directly through the interface
3. Embed videos and social media content
4. All media is optimized automatically

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Run database migrations
npm run db:studio    # Open Prisma Studio
```

### Adding Dependencies

When adding new dependencies, consider:

1. **Bundle size impact**
2. **TypeScript support**
3. **Compatibility with Next.js 14**
4. **Security implications**

### Code Style

The project uses:

- **ESLint** for code linting
- **Prettier** for code formatting
- **TypeScript** for type checking
- **Tailwind CSS** for consistent styling

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Other Platforms

The boilerplate can be deployed on:

- **Netlify**
- **Railway**
- **DigitalOcean App Platform**
- **AWS Amplify**
- **Self-hosted with Docker**

### Environment Variables for Production

Make sure to set these in your production environment:

```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-production-secret"
# ... other variables
```

## 🔒 Security

### Best Practices Implemented

- **Authentication**: Secure admin authentication with NextAuth.js
- **Authorization**: Role-based access control
- **Input Validation**: Form validation and sanitization
- **CSRF Protection**: Built-in CSRF protection
- **Environment Variables**: Sensitive data in environment variables
- **Database Security**: Parameterized queries with Prisma

### Additional Security Measures

Consider implementing:

1. **Rate limiting** for API routes
2. **Content Security Policy** headers
3. **Regular dependency updates**
4. **Database backups**
5. **SSL certificates** for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the `/docs` folder for detailed guides
- **Issues**: Report bugs and request features on GitHub
- **Community**: Join our Discord server for help and discussions

## 🙏 Acknowledgments

- **Next.js** team for the amazing framework
- **Vercel** for hosting and deployment platform
- **Tailwind CSS** for the utility-first CSS framework
- **Prisma** for the excellent ORM
- **Supabase** for the backend-as-a-service platform

---

Built with ❤️ using Next.js and modern web technologies.
