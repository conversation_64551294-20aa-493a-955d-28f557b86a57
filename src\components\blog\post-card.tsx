import Link from 'next/link'
import Image from 'next/image'
import { Calendar, User, Tag } from 'lucide-react'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { formatDate, generateExcerpt } from '@/lib/utils'
import type { BlogPost } from '@/types'

interface PostCardProps {
  post: BlogPost
  variant?: 'default' | 'featured' | 'compact'
}

export function PostCard({ post, variant = 'default' }: PostCardProps) {
  const excerpt = post.excerpt || generateExcerpt(post.content)

  if (variant === 'featured') {
    return (
      <Card className="overflow-hidden">
        <div className="md:flex">
          {post.featuredImage && (
            <div className="md:w-1/2">
              <div className="relative h-64 md:h-full">
                <Image
                  src={post.featuredImage}
                  alt={post.title}
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          )}
          <div className={`${post.featuredImage ? 'md:w-1/2' : 'w-full'}`}>
            <CardContent className="p-6">
              <div className="space-y-4">
                {post.category && (
                  <Link
                    href={`/blog/category/${post.category.slug}`}
                    className="inline-block"
                  >
                    <span
                      className="px-3 py-1 text-xs font-medium rounded-full text-white"
                      style={{ backgroundColor: post.category.color }}
                    >
                      {post.category.name}
                    </span>
                  </Link>
                )}
                
                <h2 className="text-2xl font-bold">
                  <Link
                    href={`/blog/${post.slug}`}
                    className="hover:text-primary transition-colors"
                  >
                    {post.title}
                  </Link>
                </h2>
                
                <p className="text-muted-foreground">{excerpt}</p>
                
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <User className="h-4 w-4" />
                    <span>{post.author.name}</span>
                  </div>
                  {post.publishedAt && (
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(post.publishedAt)}</span>
                    </div>
                  )}
                </div>
                
                {post.tags.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <div className="flex flex-wrap gap-2">
                      {post.tags.slice(0, 3).map((tag) => (
                        <Link
                          key={tag.slug}
                          href={`/blog/tag/${tag.slug}`}
                          className="text-xs text-muted-foreground hover:text-primary"
                        >
                          #{tag.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </div>
        </div>
      </Card>
    )
  }

  if (variant === 'compact') {
    return (
      <div className="flex space-x-4">
        {post.featuredImage && (
          <div className="flex-shrink-0">
            <div className="relative w-20 h-20">
              <Image
                src={post.featuredImage}
                alt={post.title}
                fill
                className="object-cover rounded-md"
              />
            </div>
          </div>
        )}
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium">
            <Link
              href={`/blog/${post.slug}`}
              className="hover:text-primary transition-colors"
            >
              {post.title}
            </Link>
          </h3>
          {post.publishedAt && (
            <p className="text-xs text-muted-foreground mt-1">
              {formatDate(post.publishedAt)}
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <Card className="overflow-hidden h-full">
      {post.featuredImage && (
        <div className="relative h-48">
          <Image
            src={post.featuredImage}
            alt={post.title}
            fill
            className="object-cover"
          />
        </div>
      )}
      
      <CardContent className="p-6">
        <div className="space-y-3">
          {post.category && (
            <Link
              href={`/blog/category/${post.category.slug}`}
              className="inline-block"
            >
              <span
                className="px-2 py-1 text-xs font-medium rounded-full text-white"
                style={{ backgroundColor: post.category.color }}
              >
                {post.category.name}
              </span>
            </Link>
          )}
          
          <h3 className="text-xl font-semibold">
            <Link
              href={`/blog/${post.slug}`}
              className="hover:text-primary transition-colors"
            >
              {post.title}
            </Link>
          </h3>
          
          <p className="text-muted-foreground text-sm line-clamp-3">
            {excerpt}
          </p>
        </div>
      </CardContent>
      
      <CardFooter className="px-6 py-4 pt-0">
        <div className="flex items-center justify-between w-full text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <User className="h-4 w-4" />
            <span>{post.author.name}</span>
          </div>
          {post.publishedAt && (
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(post.publishedAt)}</span>
            </div>
          )}
        </div>
      </CardFooter>
    </Card>
  )
}
