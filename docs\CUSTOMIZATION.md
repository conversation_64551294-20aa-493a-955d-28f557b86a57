# Customization Guide

This guide explains how to customize the Next.js Blog Boilerplate to match your brand and requirements.

## Theme Customization

### Color Scheme

The boilerplate uses CSS custom properties for theming. Edit `src/app/globals.css`:

```css
:root {
  --primary: 221.2 83.2% 53.3%;        /* Blue */
  --secondary: 210 40% 96%;            /* Light Gray */
  --accent: 45 93% 47%;                /* Yellow */
  --background: 0 0% 100%;             /* White */
  --foreground: 222.2 84% 4.9%;        /* Dark */
  /* ... other colors */
}
```

### Brand Colors

Update the theme configuration in `src/lib/config.ts`:

```typescript
export const defaultTheme: ThemeConfig = {
  colors: {
    primary: '#3B82F6',      // Your brand primary color
    secondary: '#64748B',    // Secondary color
    accent: '#F59E0B',       // Accent color for highlights
    background: '#FFFFFF',   // Background color
    foreground: '#0F172A',   // Text color
  },
  fonts: {
    heading: 'Inter, sans-serif',  // Heading font
    body: 'Inter, sans-serif',     // Body text font
  },
  spacing: {
    container: '1200px',           // Max container width
  },
}
```

### Typography

#### Changing Fonts

1. **Google Fonts**: Update `src/app/layout.tsx`
   ```typescript
   import { Poppins } from 'next/font/google'
   
   const poppins = Poppins({ 
     subsets: ['latin'],
     weight: ['400', '500', '600', '700']
   })
   ```

2. **Custom Fonts**: Add font files to `public/fonts/` and update CSS
   ```css
   @font-face {
     font-family: 'CustomFont';
     src: url('/fonts/custom-font.woff2') format('woff2');
   }
   ```

#### Typography Scale

Customize text sizes in `src/app/globals.css`:

```css
.prose h1 { @apply text-4xl font-bold; }
.prose h2 { @apply text-3xl font-semibold; }
.prose h3 { @apply text-2xl font-medium; }
/* ... other headings */
```

## Layout Customization

### Header

Edit `src/components/layout/header.tsx`:

```typescript
// Add/remove navigation items
const navigation = [
  { name: 'Home', href: '/' },
  { name: 'Blog', href: '/blog' },
  { name: 'About', href: '/about' },
  { name: 'Contact', href: '/contact' },  // Add new item
]

// Customize logo
<Link href="/" className="flex items-center space-x-2">
  <Image src="/logo.png" alt="Logo" width={40} height={40} />
  <span className="text-2xl font-bold text-primary">
    {siteConfig.name}
  </span>
</Link>
```

### Footer

Customize `src/components/layout/footer.tsx`:

```typescript
// Add custom footer sections
<div className="space-y-4">
  <h3 className="text-lg font-semibold">Services</h3>
  <ul className="space-y-2 text-sm">
    <li><Link href="/consulting">Consulting</Link></li>
    <li><Link href="/development">Development</Link></li>
  </ul>
</div>
```

### Homepage Sections

Edit `src/app/page.tsx` to customize homepage sections:

```typescript
// Customize hero section
<h1 className="text-4xl md:text-6xl font-bold mb-6">
  Your Custom
  <span className="text-primary"> Headline</span>
</h1>

// Add/remove feature sections
const features = [
  {
    icon: BookOpen,
    title: 'Your Feature',
    description: 'Your feature description'
  },
  // ... more features
]
```

## Component Customization

### UI Components

All UI components are in `src/components/ui/`. Customize them:

#### Button Variants

Edit `src/components/ui/button.tsx`:

```typescript
const buttonVariants = cva(
  "base-styles",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        custom: "bg-gradient-to-r from-purple-500 to-pink-500 text-white",
        // Add custom variants
      },
    },
  }
)
```

#### Card Styles

Customize `src/components/ui/card.tsx`:

```typescript
const Card = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow",
        className
      )}
      {...props}
    />
  )
)
```

### Blog Components

#### Post Card

Customize `src/components/blog/post-card.tsx`:

```typescript
// Add custom post card variants
if (variant === 'minimal') {
  return (
    <div className="space-y-2">
      <h3 className="font-medium">{post.title}</h3>
      <p className="text-sm text-muted-foreground">{excerpt}</p>
    </div>
  )
}

// Customize existing variants
if (variant === 'featured') {
  // Add custom styling for featured posts
}
```

## Site Configuration

### Basic Settings

Update `src/lib/config.ts`:

```typescript
export const siteConfig: SiteConfig = {
  name: 'Your Blog Name',
  description: 'Your unique blog description',
  url: 'https://yourdomain.com',
  ogImage: '/your-og-image.jpg',
  links: {
    twitter: 'https://twitter.com/yourusername',
    github: 'https://github.com/yourusername',
    instagram: 'https://instagram.com/yourusername',
    linkedin: 'https://linkedin.com/in/yourusername',  // Add more social links
  },
}
```

### SEO Configuration

Customize meta tags in `src/app/layout.tsx`:

```typescript
export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  keywords: ['your', 'custom', 'keywords'],
  authors: [{ name: 'Your Name', url: 'https://yourwebsite.com' }],
  // ... other SEO settings
}
```

## Content Customization

### Post Categories

Add default categories by seeding your database:

```typescript
// Create a seed script
const categories = [
  { name: 'Technology', slug: 'technology', color: '#3B82F6' },
  { name: 'Design', slug: 'design', color: '#10B981' },
  { name: 'Business', slug: 'business', color: '#F59E0B' },
]
```

### Rich Text Editor

Customize the editor in `src/components/admin/rich-text-editor.tsx`:

```typescript
// Add custom extensions
const editor = useEditor({
  extensions: [
    StarterKit,
    Image.configure({
      HTMLAttributes: {
        class: 'max-w-full h-auto rounded-lg shadow-md',  // Custom image styling
      },
    }),
    // Add more extensions
    Table.configure({
      resizable: true,
    }),
  ],
})

// Add custom toolbar buttons
<Button onClick={addTable}>
  <Table className="h-4 w-4" />
</Button>
```

## Admin Panel Customization

### Dashboard Widgets

Customize `src/app/admin/page.tsx`:

```typescript
// Add custom stats
const customStats = {
  totalSubscribers: 1250,
  monthlyViews: 45000,
  // ... other metrics
}

// Add custom dashboard cards
<Card>
  <CardHeader>
    <CardTitle>Custom Metric</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="text-2xl font-bold">{customStats.totalSubscribers}</div>
  </CardContent>
</Card>
```

### Navigation

Edit `src/app/admin/layout.tsx`:

```typescript
const navigation = [
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { name: 'Posts', href: '/admin/posts', icon: FileText },
  { name: 'Categories', href: '/admin/categories', icon: FolderOpen },
  { name: 'Analytics', href: '/admin/analytics', icon: BarChart },  // Add custom pages
  { name: 'Settings', href: '/admin/settings', icon: Settings },
]
```

## Advanced Customization

### Custom Post Types

1. **Extend Prisma Schema** (`prisma/schema.prisma`):
   ```prisma
   model Portfolio {
     id          String   @id @default(cuid())
     title       String
     description String?
     imageUrl    String?
     projectUrl  String?
     createdAt   DateTime @default(now())
   }
   ```

2. **Create API Routes** (`src/app/api/portfolio/route.ts`)

3. **Add Admin Pages** (`src/app/admin/portfolio/`)

### Custom Themes

Create theme variants:

```typescript
// src/lib/themes.ts
export const themes = {
  default: defaultTheme,
  dark: {
    ...defaultTheme,
    colors: {
      primary: '#60A5FA',
      background: '#0F172A',
      foreground: '#F8FAFC',
    }
  },
  minimal: {
    ...defaultTheme,
    colors: {
      primary: '#000000',
      secondary: '#666666',
      background: '#FFFFFF',
    }
  }
}
```

### Custom Hooks

Create reusable hooks in `src/hooks/`:

```typescript
// src/hooks/use-posts.ts
export function usePosts(category?: string) {
  const [posts, setPosts] = useState([])
  const [loading, setLoading] = useState(true)
  
  // Custom logic for fetching posts
  
  return { posts, loading }
}
```

## Deployment Customization

### Environment-Specific Configs

```typescript
// src/lib/config.ts
const isDevelopment = process.env.NODE_ENV === 'development'
const isProduction = process.env.NODE_ENV === 'production'

export const siteConfig = {
  name: process.env.NEXT_PUBLIC_SITE_NAME || 'Blog',
  url: isDevelopment 
    ? 'http://localhost:3000' 
    : 'https://yourdomain.com',
  // ... other configs
}
```

### Custom Build Process

Add custom scripts to `package.json`:

```json
{
  "scripts": {
    "build:analyze": "ANALYZE=true npm run build",
    "build:staging": "NODE_ENV=staging npm run build",
    "custom:deploy": "npm run build && npm run deploy:vercel"
  }
}
```

## Tips for Multiple Sites

### Shared Components

Create a shared component library:

```
src/
├── components/
│   ├── shared/          # Reusable across sites
│   └── site-specific/   # Site-specific components
```

### Configuration Management

Use environment variables for site-specific settings:

```env
NEXT_PUBLIC_SITE_THEME="minimal"
NEXT_PUBLIC_BRAND_COLOR="#FF6B6B"
NEXT_PUBLIC_CUSTOM_FEATURE="enabled"
```

### Conditional Features

```typescript
const showNewsletter = process.env.NEXT_PUBLIC_NEWSLETTER_ENABLED === 'true'
const customTheme = process.env.NEXT_PUBLIC_SITE_THEME || 'default'
```

This modular approach allows you to maintain multiple blog sites with different designs while sharing the core functionality.
