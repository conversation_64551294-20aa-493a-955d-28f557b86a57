import { Post, Category, Tag, User, Media, PostStatus, Role, MediaType } from '@prisma/client'

export type PostWithRelations = Post & {
  author: User
  category: Category | null
  tags: Tag[]
  media: Media[]
}

export type CategoryWithPosts = Category & {
  posts: Post[]
}

export type UserWithPosts = User & {
  posts: Post[]
}

export interface CreatePostData {
  title: string
  slug: string
  excerpt?: string
  content: string
  featuredImage?: string
  status: PostStatus
  categoryId?: string
  tags: string[]
}

export interface UpdatePostData extends Partial<CreatePostData> {
  id: string
}

export interface CreateCategoryData {
  name: string
  slug: string
  description?: string
  color?: string
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
  id: string
}

export interface MediaUploadData {
  file: File
  postId?: string
}

export interface SiteConfig {
  name: string
  description: string
  url: string
  ogImage?: string
  links: {
    twitter?: string
    github?: string
    instagram?: string
  }
}

export interface ThemeConfig {
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
  }
  fonts: {
    heading: string
    body: string
  }
  spacing: {
    container: string
  }
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt?: string
  content: string
  featuredImage?: string
  publishedAt?: Date
  author: {
    name: string
    image?: string
  }
  category?: {
    name: string
    slug: string
    color?: string
  }
  tags: Array<{
    name: string
    slug: string
  }>
}

export interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface SearchParams {
  query?: string
  category?: string
  tag?: string
  page?: number
  limit?: number
}

// Re-export Prisma types
export type {
  Post,
  Category,
  Tag,
  User,
  Media,
  PostStatus,
  Role,
  MediaType
}
