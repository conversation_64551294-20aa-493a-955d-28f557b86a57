# Database
DATABASE_URL="postgresql://username:password@localhost:5432/blog_db"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Admin credentials (for initial setup)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password"

# File upload
NEXT_PUBLIC_MAX_FILE_SIZE=5242880 # 5MB in bytes

# Site configuration
NEXT_PUBLIC_SITE_NAME="My Blog"
NEXT_PUBLIC_SITE_DESCRIPTION="A modern blog built with Next.js"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
