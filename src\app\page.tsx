import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { PostCard } from '@/components/blog/post-card'
import { <PERSON>Right, BookOpen, Users, Zap } from 'lucide-react'

// Mock data for demonstration
const featuredPost = {
  id: '1',
  title: 'Getting Started with Next.js Blog Development',
  slug: 'getting-started-nextjs-blog',
  excerpt: 'Learn how to build a modern blog with Next.js, TypeScript, and Tailwind CSS. This comprehensive guide covers everything from setup to deployment.',
  content: '<p>This is the full content of the blog post...</p>',
  featuredImage: '/api/placeholder/800/400',
  publishedAt: new Date('2024-01-15'),
  author: {
    name: '<PERSON>',
    image: '/api/placeholder/40/40'
  },
  category: {
    name: 'Development',
    slug: 'development',
    color: '#3B82F6'
  },
  tags: [
    { name: 'Next.js', slug: 'nextjs' },
    { name: 'TypeScript', slug: 'typescript' },
    { name: 'React', slug: 'react' }
  ]
}

const recentPosts = [
  {
    id: '2',
    title: 'Building Responsive Layouts with Tailwind CSS',
    slug: 'responsive-layouts-tailwind',
    excerpt: 'Master the art of creating beautiful, responsive layouts using Tailwind CSS utility classes.',
    content: '<p>Content here...</p>',
    featuredImage: '/api/placeholder/400/250',
    publishedAt: new Date('2024-01-10'),
    author: { name: 'Jane Smith', image: '/api/placeholder/40/40' },
    category: { name: 'CSS', slug: 'css', color: '#10B981' },
    tags: [{ name: 'CSS', slug: 'css' }, { name: 'Design', slug: 'design' }]
  },
  {
    id: '3',
    title: 'State Management in React Applications',
    slug: 'react-state-management',
    excerpt: 'Explore different approaches to managing state in React applications, from useState to Zustand.',
    content: '<p>Content here...</p>',
    featuredImage: '/api/placeholder/400/250',
    publishedAt: new Date('2024-01-05'),
    author: { name: 'Mike Johnson', image: '/api/placeholder/40/40' },
    category: { name: 'React', slug: 'react', color: '#F59E0B' },
    tags: [{ name: 'React', slug: 'react' }, { name: 'JavaScript', slug: 'javascript' }]
  }
]

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                Welcome to Our
                <span className="text-primary"> Blog</span>
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                Discover insights, tutorials, and stories about web development,
                design, and technology. Join our community of developers and creators.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg">
                  <Link href="/blog">
                    Explore Articles
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg" asChild>
                  <Link href="/about">Learn More</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Why Choose Our Blog?</h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                We provide high-quality content, expert insights, and a community-driven approach to learning.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Quality Content</h3>
                <p className="text-muted-foreground">
                  In-depth articles and tutorials written by industry experts and experienced developers.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Community Driven</h3>
                <p className="text-muted-foreground">
                  Join a vibrant community of developers sharing knowledge and experiences.
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Latest Trends</h3>
                <p className="text-muted-foreground">
                  Stay updated with the latest technologies, frameworks, and best practices.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Featured Post */}
        <section className="py-20 bg-muted/50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Featured Article</h2>
              <p className="text-muted-foreground">
                Don't miss our latest featured content
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <PostCard post={featuredPost} variant="featured" />
            </div>
          </div>
        </section>

        {/* Recent Posts */}
        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-12">
              <div>
                <h2 className="text-3xl font-bold mb-4">Recent Articles</h2>
                <p className="text-muted-foreground">
                  Catch up on our latest posts and insights
                </p>
              </div>
              <Button variant="outline" asChild>
                <Link href="/blog">View All</Link>
              </Button>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {recentPosts.map((post) => (
                <PostCard key={post.id} post={post} />
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
